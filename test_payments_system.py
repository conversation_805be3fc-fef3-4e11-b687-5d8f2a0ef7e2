#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام إدارة المدفوعات الشامل
Test script for the comprehensive payments management system
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

def test_payments_system():
    """اختبار النظام الأساسي"""
    print("بدء اختبار نظام إدارة المدفوعات الشامل...")
    
    try:
        # اختبار استيراد الوحدة الرئيسية
        print("1. اختبار استيراد الوحدة الرئيسية...")
        from payments_management import PaymentsManagementWindow, PaymentDialog, open_payments_management_window
        print("✓ تم استيراد الوحدة الرئيسية بنجاح")
        
        # اختبار إنشاء النافذة الرئيسية
        print("2. اختبار إنشاء النافذة الرئيسية...")
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار الوضع الشامل
        window_all = PaymentsManagementWindow(payment_type="all")
        print("✓ تم إنشاء نافذة الوضع الشامل بنجاح")
        
        # اختبار وضع المشاريع
        window_projects = PaymentsManagementWindow(payment_type="projects")
        print("✓ تم إنشاء نافذة وضع المشاريع بنجاح")
        
        # اختبار وضع العقود
        window_contracts = PaymentsManagementWindow(payment_type="contracts")
        print("✓ تم إنشاء نافذة وضع العقود بنجاح")
        
        # اختبار حوار إضافة دفعة مشروع
        print("3. اختبار حوار إضافة دفعة مشروع...")
        project_dialog = PaymentDialog(window_projects, "project")
        print("✓ تم إنشاء حوار إضافة دفعة مشروع بنجاح")
        
        # اختبار حوار إضافة دفعة عقد
        print("4. اختبار حوار إضافة دفعة عقد...")
        contract_dialog = PaymentDialog(window_contracts, "contract")
        print("✓ تم إنشاء حوار إضافة دفعة عقد بنجاح")
        
        # اختبار الدالة المساعدة
        print("5. اختبار الدالة المساعدة...")
        helper_window = open_payments_management_window(payment_type="all")
        if helper_window:
            print("✓ تم إنشاء النافذة عبر الدالة المساعدة بنجاح")
        
        print("\n" + "="*50)
        print("✅ جميع الاختبارات الأساسية نجحت!")
        print("="*50)
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\nاختبار الاتصال بقاعدة البيانات...")
    
    try:
        import mysql.connector
        from db import host, user, password
        
        # اختبار الاتصال
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password, 
            database="project_manager_V2"
        )
        cursor = conn.cursor()
        
        # اختبار وجود الجداول المطلوبة
        tables_to_check = ["المشاريع_المدفوعات", "دفعات_العقود", "المشاريع", "العقود"]
        
        for table in tables_to_check:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if cursor.fetchone():
                print(f"✓ جدول {table} موجود")
            else:
                print(f"⚠️ جدول {table} غير موجود")
        
        cursor.close()
        conn.close()
        
        print("✅ اختبار قاعدة البيانات نجح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_integration():
    """اختبار التكامل مع الأقسام الأخرى"""
    print("\nاختبار التكامل مع الأقسام الأخرى...")
    
    try:
        # اختبار التكامل مع نظام البطاقات
        print("1. اختبار التكامل مع نظام البطاقات...")
        try:
            from نظام_البطاقات import ProjectCard
            print("✓ تم العثور على نظام البطاقات")
        except ImportError:
            print("⚠️ لم يتم العثور على نظام البطاقات")
        
        # اختبار التكامل مع نظام العقود
        print("2. اختبار التكامل مع نظام العقود...")
        try:
            from العقود import ContractsApp
            print("✓ تم العثور على نظام العقود")
        except ImportError:
            print("⚠️ لم يتم العثور على نظام العقود")
        
        # اختبار التكامل مع النظام الرئيسي
        print("3. اختبار التكامل مع النظام الرئيسي...")
        try:
            from منظومة_المهندس import MainWindow
            print("✓ تم العثور على النظام الرئيسي")
        except ImportError:
            print("⚠️ لم يتم العثور على النظام الرئيسي")
        
        print("✅ اختبار التكامل اكتمل!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def run_comprehensive_test():
    """تشغيل الاختبار الشامل"""
    print("🚀 بدء الاختبار الشامل لنظام إدارة المدفوعات")
    print("="*60)
    
    results = []
    
    # تشغيل الاختبارات
    results.append(("اختبار النظام الأساسي", test_payments_system()))
    results.append(("اختبار قاعدة البيانات", test_database_connection()))
    results.append(("اختبار التكامل", test_integration()))
    
    # عرض النتائج
    print("\n" + "="*60)
    print("📊 ملخص نتائج الاختبار:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("="*60)
    print(f"النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("="*60)
    
    return passed == total

if __name__ == "__main__":
    # تشغيل الاختبار الشامل
    success = run_comprehensive_test()
    
    # إنهاء البرنامج بالكود المناسب
    sys.exit(0 if success else 1)
