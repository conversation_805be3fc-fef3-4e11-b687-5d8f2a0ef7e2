#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة المدفوعات الشامل
Comprehensive Payments Management System

يدعم إدارة مدفوعات المشاريع ودفعات العقود في واجهة موحدة
Supports managing both project payments and contract payments in a unified interface
"""

import sys
import os
from decimal import Decimal, InvalidOperation
from datetime import datetime, date
import mysql.connector
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
    QDateEdit, QTextEdit, QMessageBox, QHeaderView, QFrame, QGroupBox,
    QFormLayout, QDoubleSpinBox, QCheckBox, QTab<PERSON>idget, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
    QProgressBar, QSpacerItem, QSizePolicy, QInputDialog
)
from PySide6.QtCore import Qt, QDate, QTimer, Signal
from PySide6.QtGui import QFont, QIcon, QPixmap
import qtawesome as qta

# استيراد الوحدات المطلوبة من التطبيق
from for_all import *
from ستايل import *
from ui_boton import *
from db import *
from متغيرات import *
from tools import *


class PaymentsManagementWindow(QMainWindow):
    """النافذة الرئيسية لإدارة المدفوعات الشاملة"""
    
    def __init__(self, parent=None, payment_type="all", entity_id=None, entity_data=None):
        super().__init__(parent)
        self.parent_window = parent
        self.payment_type = payment_type  # "projects", "contracts", "all"
        self.entity_id = entity_id  # معرف المشروع أو العقد
        self.entity_data = entity_data or {}
        
        # إعداد النافذة
        self.setup_window()
        
        # إنشاء واجهة المستخدم
        self.create_ui()
        
        # تحميل البيانات
        self.load_data()
        
        # تطبيق الستايل
        apply_stylesheet(self)
    
    def setup_window(self):
        """إعداد النافذة الأساسية"""
        self.setWindowTitle("نظام إدارة المدفوعات الشامل")
        self.setGeometry(100, 100, 1400, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        
        # تعيين أيقونة النافذة
        try:
            icon_path = os.path.join(icons_dir, 'payments_icon.png')
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
            else:
                # استخدام أيقونة افتراضية
                self.setWindowIcon(qta.icon('fa5s.money-bill-wave', color='green'))
        except:
            pass
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # إنشاء شريط الأدوات العلوي
        self.create_toolbar(main_layout)
        
        # إنشاء منطقة المحتوى الرئيسية
        self.create_main_content(main_layout)
        
        # إنشاء شريط الحالة
        self.create_status_bar(main_layout)
    
    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات العلوي"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setMaximumHeight(80)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(10, 5, 10, 5)
        
        # مجموعة أزرار الإجراءات الرئيسية
        actions_group = QGroupBox("الإجراءات")
        actions_layout = QHBoxLayout(actions_group)
        
        # زر إضافة دفعة جديدة
        self.add_payment_btn = QPushButton(qta.icon('fa5s.plus', color='darkgreen'), "إضافة دفعة")
        self.add_payment_btn.setMinimumSize(120, 40)
        self.add_payment_btn.clicked.connect(self.add_new_payment)
        actions_layout.addWidget(self.add_payment_btn)
        
        # زر تعديل دفعة
        self.edit_payment_btn = QPushButton(qta.icon('fa5s.edit', color='gray'), "تعديل")
        self.edit_payment_btn.setMinimumSize(100, 40)
        self.edit_payment_btn.clicked.connect(self.edit_selected_payment)
        actions_layout.addWidget(self.edit_payment_btn)
        
        # زر حذف دفعة
        self.delete_payment_btn = QPushButton(qta.icon('fa5s.trash', color='crimson'), "حذف")
        self.delete_payment_btn.setMinimumSize(100, 40)
        self.delete_payment_btn.clicked.connect(self.delete_selected_payment)
        actions_layout.addWidget(self.delete_payment_btn)
        
        toolbar_layout.addWidget(actions_group)
        
        # مجموعة أزرار التقارير والطباعة
        reports_group = QGroupBox("التقارير")
        reports_layout = QHBoxLayout(reports_group)
        
        # زر طباعة الجدول
        self.print_table_btn = QPushButton(qta.icon('fa5s.print', color='navy'), "طباعة الجدول")
        self.print_table_btn.setMinimumSize(120, 40)
        self.print_table_btn.clicked.connect(self.print_payments_table)
        reports_layout.addWidget(self.print_table_btn)
        
        # زر سند قبض
        self.receipt_btn = QPushButton(qta.icon('fa5s.receipt', color='darkgreen'), "سند قبض")
        self.receipt_btn.setMinimumSize(100, 40)
        self.receipt_btn.clicked.connect(self.print_payment_receipt)
        reports_layout.addWidget(self.receipt_btn)
        
        # زر تصدير البيانات
        self.export_btn = QPushButton(qta.icon('fa5s.file-export', color='orange'), "تصدير")
        self.export_btn.setMinimumSize(100, 40)
        self.export_btn.clicked.connect(self.export_payments_data)
        reports_layout.addWidget(self.export_btn)
        
        toolbar_layout.addWidget(reports_group)
        
        # مجموعة التصفية والبحث
        filter_group = QGroupBox("البحث والتصفية")
        filter_layout = QHBoxLayout(filter_group)
        
        # حقل البحث
        filter_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث في الوصف، المستلم، أو طريقة الدفع...")
        self.search_edit.setMinimumWidth(200)
        self.search_edit.textChanged.connect(self.filter_payments)
        filter_layout.addWidget(self.search_edit)
        
        # تصفية حسب النوع
        filter_layout.addWidget(QLabel("النوع:"))
        self.type_filter_combo = QComboBox()
        self.type_filter_combo.addItems(["الكل", "مدفوعات المشاريع", "دفعات العقود"])
        self.type_filter_combo.currentTextChanged.connect(self.filter_payments)
        filter_layout.addWidget(self.type_filter_combo)
        
        toolbar_layout.addWidget(filter_group)
        
        # إضافة مساحة مرنة
        toolbar_layout.addStretch()
        
        parent_layout.addWidget(toolbar_frame)
    
    def create_main_content(self, parent_layout):
        """إنشاء منطقة المحتوى الرئيسية"""
        # إنشاء splitter للتقسيم بين المعلومات والجدول
        main_splitter = QSplitter(Qt.Vertical)

        # منطقة المعلومات العلوية
        self.create_info_section(main_splitter)

        # منطقة الجدول
        self.create_table_section(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([150, 500])
        main_splitter.setCollapsible(0, False)
        main_splitter.setCollapsible(1, False)

        parent_layout.addWidget(main_splitter)

    def create_info_section(self, parent_widget):
        """إنشاء قسم المعلومات العلوي"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.StyledPanel)
        info_frame.setMaximumHeight(150)

        info_layout = QHBoxLayout(info_frame)
        info_layout.setContentsMargins(10, 10, 10, 10)

        # معلومات المشروع/العقد (إذا كان محدد)
        if self.entity_id and self.entity_data:
            entity_group = QGroupBox("معلومات المشروع/العقد")
            entity_layout = QGridLayout(entity_group)

            # اسم المشروع/العقد
            entity_name = self.entity_data.get('اسم_المشروع') or self.entity_data.get('موضوع_العقد', 'غير محدد')
            entity_layout.addWidget(QLabel("الاسم:"), 0, 0)
            entity_layout.addWidget(QLabel(entity_name), 0, 1)

            # العميل/الطرف الثاني
            client_name = self.entity_data.get('اسم_العميل') or self.entity_data.get('الطرف_الثاني', 'غير محدد')
            entity_layout.addWidget(QLabel("العميل:"), 1, 0)
            entity_layout.addWidget(QLabel(client_name), 1, 1)

            info_layout.addWidget(entity_group)

        # إحصائيات المدفوعات
        stats_group = QGroupBox("إحصائيات المدفوعات")
        stats_layout = QGridLayout(stats_group)

        # إجمالي المدفوعات
        self.total_payments_label = QLabel("0.00")
        self.total_payments_label.setStyleSheet("font-weight: bold; color: #2ecc71; font-size: 14px;")
        stats_layout.addWidget(QLabel("إجمالي المدفوعات:"), 0, 0)
        stats_layout.addWidget(self.total_payments_label, 0, 1)

        # عدد المدفوعات
        self.payments_count_label = QLabel("0")
        self.payments_count_label.setStyleSheet("font-weight: bold; color: #3498db; font-size: 14px;")
        stats_layout.addWidget(QLabel("عدد المدفوعات:"), 1, 0)
        stats_layout.addWidget(self.payments_count_label, 1, 1)

        # آخر دفعة
        self.last_payment_label = QLabel("لا توجد دفعات")
        self.last_payment_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 12px;")
        stats_layout.addWidget(QLabel("آخر دفعة:"), 2, 0)
        stats_layout.addWidget(self.last_payment_label, 2, 1)

        info_layout.addWidget(stats_group)

        # تصفية متقدمة
        advanced_filter_group = QGroupBox("تصفية متقدمة")
        advanced_filter_layout = QGridLayout(advanced_filter_group)

        # تصفية حسب التاريخ
        advanced_filter_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.date_from_edit = QDateEdit()
        self.date_from_edit.setDate(QDate.currentDate().addMonths(-1))
        self.date_from_edit.setCalendarPopup(True)
        self.date_from_edit.dateChanged.connect(self.filter_payments)
        advanced_filter_layout.addWidget(self.date_from_edit, 0, 1)

        advanced_filter_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.date_to_edit = QDateEdit()
        self.date_to_edit.setDate(QDate.currentDate())
        self.date_to_edit.setCalendarPopup(True)
        self.date_to_edit.dateChanged.connect(self.filter_payments)
        advanced_filter_layout.addWidget(self.date_to_edit, 0, 3)

        # تصفية حسب المبلغ
        advanced_filter_layout.addWidget(QLabel("المبلغ من:"), 1, 0)
        self.amount_from_edit = QLineEdit()
        self.amount_from_edit.setPlaceholderText("0.00")
        self.amount_from_edit.textChanged.connect(self.filter_payments)
        advanced_filter_layout.addWidget(self.amount_from_edit, 1, 1)

        advanced_filter_layout.addWidget(QLabel("إلى:"), 1, 2)
        self.amount_to_edit = QLineEdit()
        self.amount_to_edit.setPlaceholderText("999999.99")
        self.amount_to_edit.textChanged.connect(self.filter_payments)
        advanced_filter_layout.addWidget(self.amount_to_edit, 1, 3)

        info_layout.addWidget(advanced_filter_group)

        # إضافة مساحة مرنة
        info_layout.addStretch()

        parent_widget.addWidget(info_frame)

    def create_table_section(self, parent_widget):
        """إنشاء قسم الجدول"""
        table_frame = QFrame()
        table_frame.setFrameStyle(QFrame.StyledPanel)

        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(10, 10, 10, 10)

        # عنوان الجدول
        table_title = QLabel("جدول المدفوعات")
        table_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 5px;")
        table_title.setAlignment(Qt.AlignCenter)
        table_layout.addWidget(table_title)

        # إنشاء الجدول
        self.payments_table = QTableWidget()
        self.setup_payments_table()
        table_layout.addWidget(self.payments_table)

        parent_widget.addWidget(table_frame)

    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_frame.setMaximumHeight(40)

        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 5, 10, 5)

        # رسالة الحالة
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        status_layout.addWidget(self.status_label)

        # شريط التقدم (مخفي افتراضياً)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_layout.addWidget(self.progress_bar)

        # إضافة مساحة مرنة
        status_layout.addStretch()

        # معلومات إضافية
        self.info_label = QLabel("")
        self.info_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        status_layout.addWidget(self.info_label)

        parent_layout.addWidget(status_frame)
    
    def setup_payments_table(self):
        """إعداد جدول المدفوعات"""
        # تحديد أعمدة الجدول حسب نوع المدفوعات
        if self.payment_type == "projects":
            headers = [
                "ID", "الرقم", "نوع الدفعة", "اسم المشروع", "اسم العميل",
                "وصف الدفعة", "المبلغ المدفوع", "تاريخ الدفع", "طريقة الدفع",
                "المستلم", "الخصم", "المستخدم", "تاريخ الإضافة"
            ]
        elif self.payment_type == "contracts":
            headers = [
                "ID", "الرقم", "نوع الدفعة", "موضوع العقد", "الطرف الثاني",
                "وصف الدفعة", "المبلغ", "تاريخ الدفعة", "حالة الدفعة",
                "تاريخ السداد", "ملاحظات", "المستخدم", "تاريخ الإضافة"
            ]
        else:  # all
            headers = [
                "ID", "الرقم", "نوع الدفعة", "اسم المشروع/العقد", "العميل/الطرف الثاني",
                "وصف الدفعة", "المبلغ", "تاريخ الدفع", "طريقة الدفع/الحالة",
                "المستلم/تاريخ السداد", "ملاحظات", "المستخدم", "تاريخ الإضافة"
            ]

        self.payments_table.setColumnCount(len(headers))
        self.payments_table.setHorizontalHeaderLabels(headers)

        # إخفاء عمود ID
        self.payments_table.hideColumn(0)

        # تطبيق إعدادات الجدول
        table_setting(self.payments_table)

        # ربط النقر المزدوج بتعديل الدفعة
        self.payments_table.itemDoubleClicked.connect(self.edit_selected_payment)

        # ربط تغيير التحديد بتحديث الأزرار
        self.payments_table.itemSelectionChanged.connect(self.update_buttons_state)

    def load_data(self):
        """تحميل بيانات المدفوعات"""
        try:
            self.status_label.setText("جاري تحميل البيانات...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # الاتصال بقاعدة البيانات
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            payments_data = []

            # تحميل مدفوعات المشاريع
            if self.payment_type in ["projects", "all"]:
                self.progress_bar.setValue(25)
                project_payments = self.load_project_payments(cursor)
                payments_data.extend(project_payments)

            # تحميل دفعات العقود
            if self.payment_type in ["contracts", "all"]:
                self.progress_bar.setValue(50)
                contract_payments = self.load_contract_payments(cursor)
                payments_data.extend(contract_payments)

            self.progress_bar.setValue(75)

            # ملء الجدول
            self.populate_table(payments_data)

            # تحديث الإحصائيات
            self.update_statistics(payments_data)

            self.progress_bar.setValue(100)
            self.progress_bar.setVisible(False)
            self.status_label.setText(f"تم تحميل {len(payments_data)} دفعة بنجاح")

            cursor.close()
            conn.close()

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.status_label.setText("خطأ في تحميل البيانات")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المدفوعات:\n{str(e)}")

    def load_project_payments(self, cursor):
        """تحميل مدفوعات المشاريع"""
        try:
            if self.entity_id and self.payment_type == "projects":
                # تحميل مدفوعات مشروع محدد
                query = """
                    SELECT p.id, p.معرف_المشروع, p.معرف_العميل, p.المبلغ_المدفوع,
                           p.وصف_المدفوع, p.تاريخ_الدفع, p.طريقة_الدفع, p.المستلم,
                           p.خصم, p.المستخدم, p.تاريخ_الإضافة,
                           pr.اسم_المشروع, c.اسم_العميل
                    FROM المشاريع_المدفوعات p
                    LEFT JOIN المشاريع pr ON p.معرف_المشروع = pr.id
                    LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                    WHERE p.معرف_المشروع = %s
                    ORDER BY p.تاريخ_الدفع DESC, p.id DESC
                """
                cursor.execute(query, (self.entity_id,))
            else:
                # تحميل جميع مدفوعات المشاريع
                query = """
                    SELECT p.id, p.معرف_المشروع, p.معرف_العميل, p.المبلغ_المدفوع,
                           p.وصف_المدفوع, p.تاريخ_الدفع, p.طريقة_الدفع, p.المستلم,
                           p.خصم, p.المستخدم, p.تاريخ_الإضافة,
                           pr.اسم_المشروع, c.اسم_العميل
                    FROM المشاريع_المدفوعات p
                    LEFT JOIN المشاريع pr ON p.معرف_المشروع = pr.id
                    LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                    ORDER BY p.تاريخ_الدفع DESC, p.id DESC
                """
                cursor.execute(query)

            results = cursor.fetchall()
            payments = []

            for row in results:
                payment = {
                    'id': row[0],
                    'type': 'project',
                    'project_id': row[1],
                    'client_id': row[2],
                    'amount': row[3] or 0,
                    'description': row[4] or '',
                    'payment_date': row[5],
                    'payment_method': row[6] or '',
                    'receiver': row[7] or '',
                    'discount': row[8] or 0,
                    'user': row[9] or '',
                    'created_date': row[10],
                    'project_name': row[11] or 'غير محدد',
                    'client_name': row[12] or 'غير محدد'
                }
                payments.append(payment)

            return payments

        except Exception as e:
            print(f"خطأ في تحميل مدفوعات المشاريع: {e}")
            return []
    
    def load_contract_payments(self, cursor):
        """تحميل دفعات العقود"""
        try:
            if self.entity_id and self.payment_type == "contracts":
                # تحميل دفعات عقد محدد
                query = """
                    SELECT cp.id, cp.معرف_العقد, cp.وصف_الدفعة, cp.المبلغ,
                           cp.تاريخ_الدفعة, cp.حالة_الدفعة, cp.تاريخ_السداد,
                           cp.ملاحظات, cp.المستخدم, cp.تاريخ_الإضافة,
                           c.موضوع_العقد, c.الطرف_الثاني
                    FROM دفعات_العقود cp
                    LEFT JOIN العقود c ON cp.معرف_العقد = c.id
                    WHERE cp.معرف_العقد = %s
                    ORDER BY cp.تاريخ_الدفعة DESC, cp.id DESC
                """
                cursor.execute(query, (self.entity_id,))
            else:
                # تحميل جميع دفعات العقود
                query = """
                    SELECT cp.id, cp.معرف_العقد, cp.وصف_الدفعة, cp.المبلغ,
                           cp.تاريخ_الدفعة, cp.حالة_الدفعة, cp.تاريخ_السداد,
                           cp.ملاحظات, cp.المستخدم, cp.تاريخ_الإضافة,
                           c.موضوع_العقد, c.الطرف_الثاني
                    FROM دفعات_العقود cp
                    LEFT JOIN العقود c ON cp.معرف_العقد = c.id
                    ORDER BY cp.تاريخ_الدفعة DESC, cp.id DESC
                """
                cursor.execute(query)

            results = cursor.fetchall()
            payments = []

            for row in results:
                payment = {
                    'id': row[0],
                    'type': 'contract',
                    'contract_id': row[1],
                    'description': row[2] or '',
                    'amount': row[3] or 0,
                    'payment_date': row[4],
                    'status': row[5] or 'غير مدفوعة',
                    'actual_payment_date': row[6],
                    'notes': row[7] or '',
                    'user': row[8] or '',
                    'created_date': row[9],
                    'contract_subject': row[10] or 'غير محدد',
                    'second_party': row[11] or 'غير محدد'
                }
                payments.append(payment)

            return payments

        except Exception as e:
            print(f"خطأ في تحميل دفعات العقود: {e}")
            return []

    def populate_table(self, payments_data):
        """ملء الجدول بالبيانات"""
        try:
            self.payments_table.setRowCount(len(payments_data))

            for row, payment in enumerate(payments_data):
                # ID (مخفي)
                item = QTableWidgetItem(str(payment['id']))
                item.setData(Qt.UserRole, payment)  # حفظ البيانات الكاملة
                self.payments_table.setItem(row, 0, item)

                # الرقم
                self.payments_table.setItem(row, 1, QTableWidgetItem(str(row + 1)))

                # نوع الدفعة
                payment_type_text = "مدفوعات المشاريع" if payment['type'] == 'project' else "دفعات العقود"
                self.payments_table.setItem(row, 2, QTableWidgetItem(payment_type_text))

                if self.payment_type == "projects" or (self.payment_type == "all" and payment['type'] == 'project'):
                    # مدفوعات المشاريع
                    self.payments_table.setItem(row, 3, QTableWidgetItem(payment['project_name']))
                    self.payments_table.setItem(row, 4, QTableWidgetItem(payment['client_name']))
                    self.payments_table.setItem(row, 5, QTableWidgetItem(payment['description']))

                    amount_item = QTableWidgetItem(f"{payment['amount']:,.2f}")
                    amount_item.setTextAlignment(Qt.AlignCenter)
                    self.payments_table.setItem(row, 6, amount_item)

                    date_item = QTableWidgetItem(str(payment['payment_date']) if payment['payment_date'] else '')
                    date_item.setTextAlignment(Qt.AlignCenter)
                    self.payments_table.setItem(row, 7, date_item)

                    self.payments_table.setItem(row, 8, QTableWidgetItem(payment['payment_method']))
                    self.payments_table.setItem(row, 9, QTableWidgetItem(payment['receiver']))

                    discount_item = QTableWidgetItem(f"{payment['discount']:,.2f}" if payment['discount'] else "0.00")
                    discount_item.setTextAlignment(Qt.AlignCenter)
                    self.payments_table.setItem(row, 10, discount_item)

                    self.payments_table.setItem(row, 11, QTableWidgetItem(payment['user']))

                    created_date_item = QTableWidgetItem(str(payment['created_date']) if payment['created_date'] else '')
                    created_date_item.setTextAlignment(Qt.AlignCenter)
                    self.payments_table.setItem(row, 12, created_date_item)

                elif self.payment_type == "contracts" or (self.payment_type == "all" and payment['type'] == 'contract'):
                    # دفعات العقود
                    if self.payment_type == "all":
                        # في الوضع الشامل، استخدم الأعمدة الموحدة
                        self.payments_table.setItem(row, 3, QTableWidgetItem(payment['contract_subject']))
                        self.payments_table.setItem(row, 4, QTableWidgetItem(payment['second_party']))
                        self.payments_table.setItem(row, 5, QTableWidgetItem(payment['description']))

                        amount_item = QTableWidgetItem(f"{payment['amount']:,.2f}")
                        amount_item.setTextAlignment(Qt.AlignCenter)
                        self.payments_table.setItem(row, 6, amount_item)

                        date_item = QTableWidgetItem(str(payment['payment_date']) if payment['payment_date'] else '')
                        date_item.setTextAlignment(Qt.AlignCenter)
                        self.payments_table.setItem(row, 7, date_item)

                        self.payments_table.setItem(row, 8, QTableWidgetItem(payment['status']))

                        actual_date = str(payment['actual_payment_date']) if payment['actual_payment_date'] else 'غير محدد'
                        self.payments_table.setItem(row, 9, QTableWidgetItem(actual_date))

                        self.payments_table.setItem(row, 10, QTableWidgetItem(payment['notes']))
                        self.payments_table.setItem(row, 11, QTableWidgetItem(payment['user']))

                        created_date_item = QTableWidgetItem(str(payment['created_date']) if payment['created_date'] else '')
                        created_date_item.setTextAlignment(Qt.AlignCenter)
                        self.payments_table.setItem(row, 12, created_date_item)
                    else:
                        # وضع العقود فقط
                        self.payments_table.setItem(row, 3, QTableWidgetItem(payment['contract_subject']))
                        self.payments_table.setItem(row, 4, QTableWidgetItem(payment['second_party']))
                        self.payments_table.setItem(row, 5, QTableWidgetItem(payment['description']))

                        amount_item = QTableWidgetItem(f"{payment['amount']:,.2f}")
                        amount_item.setTextAlignment(Qt.AlignCenter)
                        self.payments_table.setItem(row, 6, amount_item)

                        date_item = QTableWidgetItem(str(payment['payment_date']) if payment['payment_date'] else '')
                        date_item.setTextAlignment(Qt.AlignCenter)
                        self.payments_table.setItem(row, 7, date_item)

                        self.payments_table.setItem(row, 8, QTableWidgetItem(payment['status']))

                        actual_date = str(payment['actual_payment_date']) if payment['actual_payment_date'] else ''
                        actual_date_item = QTableWidgetItem(actual_date)
                        actual_date_item.setTextAlignment(Qt.AlignCenter)
                        self.payments_table.setItem(row, 9, actual_date_item)

                        self.payments_table.setItem(row, 10, QTableWidgetItem(payment['notes']))
                        self.payments_table.setItem(row, 11, QTableWidgetItem(payment['user']))

                        created_date_item = QTableWidgetItem(str(payment['created_date']) if payment['created_date'] else '')
                        created_date_item.setTextAlignment(Qt.AlignCenter)
                        self.payments_table.setItem(row, 12, created_date_item)

            # تحديث حالة الأزرار
            self.update_buttons_state()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في ملء الجدول:\n{str(e)}")

    def add_new_payment(self):
        """إضافة دفعة جديدة"""
        try:
            # تحديد نوع الدفعة المطلوب إضافتها
            if self.payment_type == "projects":
                payment_type = "project"
            elif self.payment_type == "contracts":
                payment_type = "contract"
            else:
                # في الوضع الشامل، اسأل المستخدم عن النوع
                payment_type = self.ask_payment_type()
                if not payment_type:
                    return

            # فتح حوار إضافة الدفعة
            dialog = PaymentDialog(self, payment_type, entity_id=self.entity_id, entity_data=self.entity_data)
            if dialog.exec() == QDialog.Accepted:
                # تحديث البيانات
                self.load_data()
                self.status_label.setText("تم إضافة الدفعة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الدفعة:\n{str(e)}")

    def ask_payment_type(self):
        """سؤال المستخدم عن نوع الدفعة المراد إضافتها"""
        from PySide6.QtWidgets import QInputDialog

        items = ["مدفوعات المشاريع", "دفعات العقود"]
        item, ok = QInputDialog.getItem(self, "نوع الدفعة", "اختر نوع الدفعة المراد إضافتها:", items, 0, False)

        if ok and item:
            return "project" if item == "مدفوعات المشاريع" else "contract"
        return None
    
    def edit_selected_payment(self):
        """تعديل الدفعة المحددة"""
        try:
            selected_items = self.payments_table.selectedItems()
            if not selected_items:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة للتعديل")
                return

            # الحصول على بيانات الدفعة المحددة
            current_row = self.payments_table.currentRow()
            payment_data = self.payments_table.item(current_row, 0).data(Qt.UserRole)

            if not payment_data:
                QMessageBox.warning(self, "تحذير", "لا يمكن الحصول على بيانات الدفعة")
                return

            # فتح حوار التعديل
            dialog = PaymentDialog(self, payment_data['type'], payment_data=payment_data)
            if dialog.exec() == QDialog.Accepted:
                # تحديث البيانات
                self.load_data()
                self.status_label.setText("تم تعديل الدفعة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل الدفعة:\n{str(e)}")

    def delete_selected_payment(self):
        """حذف الدفعة المحددة"""
        try:
            selected_items = self.payments_table.selectedItems()
            if not selected_items:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة للحذف")
                return

            # الحصول على بيانات الدفعة المحددة
            current_row = self.payments_table.currentRow()
            payment_data = self.payments_table.item(current_row, 0).data(Qt.UserRole)

            if not payment_data:
                QMessageBox.warning(self, "تحذير", "لا يمكن الحصول على بيانات الدفعة")
                return

            # تأكيد الحذف
            payment_desc = payment_data.get('description', 'غير محدد')
            amount = payment_data.get('amount', 0)

            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الدفعة؟\n\nالوصف: {payment_desc}\nالمبلغ: {amount:,.2f} {Currency_type}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف الدفعة من قاعدة البيانات
                if self.delete_payment_from_database(payment_data):
                    # تحديث البيانات
                    self.load_data()
                    self.status_label.setText("تم حذف الدفعة بنجاح")
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حذف الدفعة من قاعدة البيانات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف الدفعة:\n{str(e)}")

    def delete_payment_from_database(self, payment_data):
        """حذف الدفعة من قاعدة البيانات"""
        try:
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            if payment_data['type'] == 'project':
                # حذف من جدول مدفوعات المشاريع
                cursor.execute("DELETE FROM المشاريع_المدفوعات WHERE id = %s", (payment_data['id'],))
            else:
                # حذف من جدول دفعات العقود
                cursor.execute("DELETE FROM دفعات_العقود WHERE id = %s", (payment_data['id'],))

            conn.commit()
            cursor.close()
            conn.close()

            return True

        except Exception as e:
            print(f"خطأ في حذف الدفعة من قاعدة البيانات: {e}")
            return False

    def filter_payments(self):
        """تصفية المدفوعات"""
        try:
            search_text = self.search_edit.text().lower()
            type_filter = self.type_filter_combo.currentText()

            # تصفية حسب التاريخ
            date_from = self.date_from_edit.date().toPython()
            date_to = self.date_to_edit.date().toPython()

            # تصفية حسب المبلغ
            amount_from_text = self.amount_from_edit.text().strip()
            amount_to_text = self.amount_to_edit.text().strip()

            try:
                amount_from = float(amount_from_text) if amount_from_text else 0
                amount_to = float(amount_to_text) if amount_to_text else float('inf')
            except ValueError:
                amount_from = 0
                amount_to = float('inf')

            # تطبيق التصفية على كل صف
            for row in range(self.payments_table.rowCount()):
                show_row = True

                # الحصول على بيانات الصف
                payment_data = self.payments_table.item(row, 0).data(Qt.UserRole)
                if not payment_data:
                    continue

                # تصفية حسب النص
                if search_text:
                    description = payment_data.get('description', '').lower()
                    receiver = payment_data.get('receiver', '').lower()
                    payment_method = payment_data.get('payment_method', '').lower()
                    notes = payment_data.get('notes', '').lower()

                    if not any(search_text in text for text in [description, receiver, payment_method, notes]):
                        show_row = False

                # تصفية حسب النوع
                if type_filter != "الكل":
                    if type_filter == "مدفوعات المشاريع" and payment_data['type'] != 'project':
                        show_row = False
                    elif type_filter == "دفعات العقود" and payment_data['type'] != 'contract':
                        show_row = False

                # تصفية حسب التاريخ
                payment_date = payment_data.get('payment_date')
                if payment_date:
                    if isinstance(payment_date, str):
                        try:
                            payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()
                        except:
                            payment_date = None

                    if payment_date and not (date_from <= payment_date <= date_to):
                        show_row = False

                # تصفية حسب المبلغ
                amount = payment_data.get('amount', 0)
                if not (amount_from <= amount <= amount_to):
                    show_row = False

                # إظهار أو إخفاء الصف
                self.payments_table.setRowHidden(row, not show_row)

            # تحديث عدد النتائج المعروضة
            visible_rows = sum(1 for row in range(self.payments_table.rowCount())
                             if not self.payments_table.isRowHidden(row))
            self.info_label.setText(f"عرض {visible_rows} من {self.payments_table.rowCount()} دفعة")

        except Exception as e:
            print(f"خطأ في تصفية المدفوعات: {e}")

    def update_statistics(self, payments_data):
        """تحديث الإحصائيات"""
        try:
            if not payments_data:
                self.total_payments_label.setText("0.00")
                self.payments_count_label.setText("0")
                self.last_payment_label.setText("لا توجد دفعات")
                return

            # حساب إجمالي المدفوعات
            total_amount = sum(payment.get('amount', 0) for payment in payments_data)
            self.total_payments_label.setText(f"{total_amount:,.2f} {Currency_type}")

            # عدد المدفوعات
            self.payments_count_label.setText(str(len(payments_data)))

            # آخر دفعة
            if payments_data:
                last_payment = payments_data[0]  # البيانات مرتبة حسب التاريخ تنازلياً
                last_date = last_payment.get('payment_date', 'غير محدد')
                last_amount = last_payment.get('amount', 0)
                self.last_payment_label.setText(f"{last_date} - {last_amount:,.2f} {Currency_type}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def update_buttons_state(self):
        """تحديث حالة الأزرار حسب التحديد"""
        has_selection = bool(self.payments_table.selectedItems())
        self.edit_payment_btn.setEnabled(has_selection)
        self.delete_payment_btn.setEnabled(has_selection)
        self.receipt_btn.setEnabled(has_selection)
    
    def print_payments_table(self):
        """طباعة جدول المدفوعات"""
        try:
            # جمع البيانات المرئية فقط (غير المخفية)
            visible_data = []
            headers = []

            # جمع العناوين
            for col in range(1, self.payments_table.columnCount()):  # تخطي عمود ID
                if not self.payments_table.isColumnHidden(col):
                    headers.append(self.payments_table.horizontalHeaderItem(col).text())

            # جمع البيانات المرئية
            for row in range(self.payments_table.rowCount()):
                if not self.payments_table.isRowHidden(row):
                    row_data = []
                    for col in range(1, self.payments_table.columnCount()):
                        if not self.payments_table.isColumnHidden(col):
                            item = self.payments_table.item(row, col)
                            row_data.append(item.text() if item else "")
                    visible_data.append(row_data)

            if not visible_data:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات لطباعتها")
                return

            # إنشاء تقرير الطباعة
            self.create_payments_report(headers, visible_data)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة الجدول:\n{str(e)}")

    def create_payments_report(self, headers, data):
        """إنشاء تقرير المدفوعات للطباعة"""
        try:
            from PySide6.QtPrintSupport import QPrinter, QPrintDialog
            from PySide6.QtGui import QPainter, QFont, QPen
            from PySide6.QtCore import QRect

            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Landscape)

            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخطوط
                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 10, QFont.Bold)
                data_font = QFont("Arial", 9)

                # رسم العنوان
                painter.setFont(title_font)
                title = "تقرير المدفوعات الشامل"
                title_rect = painter.boundingRect(0, 0, printer.width(), 100, Qt.AlignCenter, title)
                painter.drawText(title_rect, Qt.AlignCenter, title)

                # رسم التاريخ
                painter.setFont(data_font)
                date_text = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
                painter.drawText(printer.width() - 200, title_rect.bottom() + 20, date_text)

                # حساب عرض الأعمدة
                col_width = (printer.width() - 100) // len(headers)
                row_height = 30
                start_y = title_rect.bottom() + 60

                # رسم العناوين
                painter.setFont(header_font)
                painter.setPen(QPen(Qt.black, 2))
                for i, header in enumerate(headers):
                    x = 50 + i * col_width
                    rect = QRect(x, start_y, col_width, row_height)
                    painter.drawRect(rect)
                    painter.drawText(rect, Qt.AlignCenter, header)

                # رسم البيانات
                painter.setFont(data_font)
                painter.setPen(QPen(Qt.black, 1))

                for row_idx, row_data in enumerate(data):
                    y = start_y + (row_idx + 1) * row_height

                    # التحقق من الصفحة الجديدة
                    if y + row_height > printer.height() - 100:
                        printer.newPage()
                        y = 100

                    for col_idx, cell_data in enumerate(row_data):
                        x = 50 + col_idx * col_width
                        rect = QRect(x, y, col_width, row_height)
                        painter.drawRect(rect)
                        painter.drawText(rect, Qt.AlignCenter, str(cell_data))

                painter.end()
                QMessageBox.information(self, "نجاح", "تم طباعة التقرير بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير الطباعة:\n{str(e)}")

    def print_payment_receipt(self):
        """طباعة سند قبض"""
        try:
            selected_items = self.payments_table.selectedItems()
            if not selected_items:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة لطباعة سند القبض")
                return

            # الحصول على بيانات الدفعة المحددة
            current_row = self.payments_table.currentRow()
            payment_data = self.payments_table.item(current_row, 0).data(Qt.UserRole)

            if not payment_data:
                QMessageBox.warning(self, "تحذير", "لا يمكن الحصول على بيانات الدفعة")
                return

            # إنشاء سند القبض
            self.create_payment_receipt(payment_data)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة سند القبض:\n{str(e)}")

    def create_payment_receipt(self, payment_data):
        """إنشاء سند قبض للطباعة"""
        try:
            from PySide6.QtPrintSupport import QPrinter, QPrintDialog
            from PySide6.QtGui import QPainter, QFont, QPen
            from PySide6.QtCore import QRect

            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Portrait)

            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخطوط
                title_font = QFont("Arial", 18, QFont.Bold)
                header_font = QFont("Arial", 12, QFont.Bold)
                data_font = QFont("Arial", 11)

                y_pos = 100

                # رسم العنوان
                painter.setFont(title_font)
                title = "سند قبض"
                title_rect = painter.boundingRect(0, y_pos, printer.width(), 50, Qt.AlignCenter, title)
                painter.drawText(title_rect, Qt.AlignCenter, title)
                y_pos = title_rect.bottom() + 40

                # رسم رقم السند والتاريخ
                painter.setFont(data_font)
                receipt_number = f"رقم السند: {payment_data.get('id', 'غير محدد')}"
                receipt_date = f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}"

                painter.drawText(100, y_pos, receipt_number)
                painter.drawText(printer.width() - 200, y_pos, receipt_date)
                y_pos += 60

                # رسم بيانات الدفعة
                painter.setFont(header_font)
                painter.drawText(100, y_pos, "بيانات الدفعة:")
                y_pos += 40

                painter.setFont(data_font)

                # المبلغ
                amount_text = f"المبلغ: {payment_data.get('amount', 0):,.2f} {Currency_type}"
                painter.drawText(100, y_pos, amount_text)
                y_pos += 30

                # الوصف
                description = payment_data.get('description', 'غير محدد')
                painter.drawText(100, y_pos, f"الوصف: {description}")
                y_pos += 30

                # التاريخ
                payment_date = payment_data.get('payment_date', 'غير محدد')
                painter.drawText(100, y_pos, f"تاريخ الدفع: {payment_date}")
                y_pos += 30

                # طريقة الدفع أو الحالة
                if payment_data['type'] == 'project':
                    method = payment_data.get('payment_method', 'غير محدد')
                    painter.drawText(100, y_pos, f"طريقة الدفع: {method}")
                    y_pos += 30

                    receiver = payment_data.get('receiver', 'غير محدد')
                    painter.drawText(100, y_pos, f"المستلم: {receiver}")
                else:
                    status = payment_data.get('status', 'غير محدد')
                    painter.drawText(100, y_pos, f"حالة الدفعة: {status}")

                y_pos += 60

                # خط الفصل
                painter.setPen(QPen(Qt.black, 2))
                painter.drawLine(100, y_pos, printer.width() - 100, y_pos)
                y_pos += 40

                # التوقيع
                painter.setFont(header_font)
                painter.drawText(100, y_pos, "المستلم: ________________")
                painter.drawText(printer.width() - 300, y_pos, "التوقيع: ________________")

                painter.end()
                QMessageBox.information(self, "نجاح", "تم طباعة سند القبض بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء سند القبض:\n{str(e)}")

    def export_payments_data(self):
        """تصدير بيانات المدفوعات"""
        try:
            from PySide6.QtWidgets import QFileDialog
            import csv
            import json

            # اختيار نوع التصدير
            export_types = ["Excel (*.xlsx)", "CSV (*.csv)", "JSON (*.json)"]
            export_type, ok = QInputDialog.getItem(self, "نوع التصدير", "اختر نوع التصدير:", export_types, 0, False)

            if not ok:
                return

            # اختيار مكان الحفظ
            if "Excel" in export_type:
                file_path, _ = QFileDialog.getSaveFileName(self, "حفظ ملف Excel", "payments_export.xlsx", "Excel Files (*.xlsx)")
                if file_path:
                    self.export_to_excel(file_path)
            elif "CSV" in export_type:
                file_path, _ = QFileDialog.getSaveFileName(self, "حفظ ملف CSV", "payments_export.csv", "CSV Files (*.csv)")
                if file_path:
                    self.export_to_csv(file_path)
            elif "JSON" in export_type:
                file_path, _ = QFileDialog.getSaveFileName(self, "حفظ ملف JSON", "payments_export.json", "JSON Files (*.json)")
                if file_path:
                    self.export_to_json(file_path)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات:\n{str(e)}")

    def export_to_excel(self, file_path):
        """تصدير البيانات إلى Excel"""
        try:
            import pandas as pd

            # جمع البيانات المرئية
            data = []
            headers = []

            # جمع العناوين
            for col in range(1, self.payments_table.columnCount()):
                if not self.payments_table.isColumnHidden(col):
                    headers.append(self.payments_table.horizontalHeaderItem(col).text())

            # جمع البيانات
            for row in range(self.payments_table.rowCount()):
                if not self.payments_table.isRowHidden(row):
                    row_data = []
                    for col in range(1, self.payments_table.columnCount()):
                        if not self.payments_table.isColumnHidden(col):
                            item = self.payments_table.item(row, col)
                            row_data.append(item.text() if item else "")
                    data.append(row_data)

            # إنشاء DataFrame وحفظه
            df = pd.DataFrame(data, columns=headers)
            df.to_excel(file_path, index=False, engine='openpyxl')

            QMessageBox.information(self, "نجاح", f"تم تصدير البيانات إلى:\n{file_path}")

        except ImportError:
            QMessageBox.critical(self, "خطأ", "مكتبة pandas غير مثبتة. يرجى تثبيتها أولاً:\npip install pandas openpyxl")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات إلى Excel:\n{str(e)}")

    def export_to_csv(self, file_path):
        """تصدير البيانات إلى CSV"""
        try:
            import csv

            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # كتابة العناوين
                headers = []
                for col in range(1, self.payments_table.columnCount()):
                    if not self.payments_table.isColumnHidden(col):
                        headers.append(self.payments_table.horizontalHeaderItem(col).text())
                writer.writerow(headers)

                # كتابة البيانات
                for row in range(self.payments_table.rowCount()):
                    if not self.payments_table.isRowHidden(row):
                        row_data = []
                        for col in range(1, self.payments_table.columnCount()):
                            if not self.payments_table.isColumnHidden(col):
                                item = self.payments_table.item(row, col)
                                row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

            QMessageBox.information(self, "نجاح", f"تم تصدير البيانات إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات إلى CSV:\n{str(e)}")

    def export_to_json(self, file_path):
        """تصدير البيانات إلى JSON"""
        try:
            import json

            # جمع البيانات الكاملة مع التفاصيل
            export_data = {
                "export_date": datetime.now().isoformat(),
                "payment_type": self.payment_type,
                "total_payments": 0,
                "payments": []
            }

            total_amount = 0
            for row in range(self.payments_table.rowCount()):
                if not self.payments_table.isRowHidden(row):
                    payment_data = self.payments_table.item(row, 0).data(Qt.UserRole)
                    if payment_data:
                        # تنظيف البيانات للتصدير
                        clean_data = {}
                        for key, value in payment_data.items():
                            if isinstance(value, (str, int, float, bool)) or value is None:
                                clean_data[key] = value
                            else:
                                clean_data[key] = str(value)

                        export_data["payments"].append(clean_data)
                        total_amount += payment_data.get('amount', 0)

            export_data["total_payments"] = len(export_data["payments"])
            export_data["total_amount"] = total_amount

            # حفظ الملف
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

            QMessageBox.information(self, "نجاح", f"تم تصدير البيانات إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات إلى JSON:\n{str(e)}")


class PaymentDialog(QDialog):
    """حوار إضافة/تعديل الدفعات"""

    def __init__(self, parent=None, payment_type="project", payment_data=None, entity_id=None, entity_data=None):
        super().__init__(parent)
        self.parent_window = parent
        self.payment_type = payment_type  # "project" or "contract"
        self.payment_data = payment_data  # للتعديل
        self.entity_id = entity_id
        self.entity_data = entity_data or {}
        self.is_edit_mode = payment_data is not None

        # إعداد الحوار
        self.setup_dialog()

        # إنشاء واجهة المستخدم
        self.create_ui()

        # تحميل البيانات للتعديل
        if self.is_edit_mode:
            self.load_payment_data()

        # تطبيق الستايل
        apply_stylesheet(self)

    def setup_dialog(self):
        """إعداد الحوار"""
        title = "تعديل الدفعة" if self.is_edit_mode else "إضافة دفعة جديدة"
        if self.payment_type == "project":
            title += " - مشروع"
        else:
            title += " - عقد"

        self.setWindowTitle(title)
        self.setGeometry(200, 200, 600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # تعيين أيقونة الحوار
        try:
            if self.is_edit_mode:
                self.setWindowIcon(qta.icon('fa5s.edit', color='gray'))
            else:
                self.setWindowIcon(qta.icon('fa5s.plus', color='darkgreen'))
        except:
            pass

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان الحوار
        title_label = QLabel(self.windowTitle())
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إنشاء نموذج البيانات
        self.create_form(layout)

        # أزرار الحفظ والإلغاء
        self.create_buttons(layout)

    def create_form(self, parent_layout):
        """إنشاء نموذج البيانات"""
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QFormLayout(form_frame)
        form_layout.setContentsMargins(15, 15, 15, 15)
        form_layout.setVerticalSpacing(10)

        if self.payment_type == "project":
            self.create_project_payment_form(form_layout)
        else:
            self.create_contract_payment_form(form_layout)

        parent_layout.addWidget(form_frame)

    def create_project_payment_form(self, form_layout):
        """إنشاء نموذج مدفوعات المشاريع"""
        # اختيار المشروع (إذا لم يكن محدد)
        if not self.entity_id:
            form_layout.addRow(QLabel("اختيار المشروع:"))
            self.project_combo = QComboBox()
            self.project_combo.setEditable(True)
            self.project_combo.lineEdit().setAlignment(Qt.AlignCenter)
            self.load_projects_combo()
            form_layout.addRow("المشروع:", self.project_combo)
        else:
            # عرض اسم المشروع المحدد
            project_name = self.entity_data.get('اسم_المشروع', f'مشروع رقم {self.entity_id}')
            project_label = QLabel(project_name)
            project_label.setStyleSheet("font-weight: bold; color: #2980b9;")
            form_layout.addRow("المشروع:", project_label)

        # المبلغ المدفوع
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("أدخل المبلغ المدفوع")
        self.amount_edit.setAlignment(Qt.AlignCenter)
        form_layout.addRow("المبلغ المدفوع:", self.amount_edit)

        # وصف الدفعة
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف الدفعة (اختياري)")
        self.description_edit.setAlignment(Qt.AlignCenter)
        form_layout.addRow("وصف الدفعة:", self.description_edit)

        # تاريخ الدفع
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setDate(QDate.currentDate())
        self.payment_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ الدفع:", self.payment_date_edit)

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.setEditable(True)
        self.payment_method_combo.lineEdit().setAlignment(Qt.AlignCenter)
        payment_methods = ["نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان", "أخرى"]
        self.payment_method_combo.addItems(payment_methods)
        form_layout.addRow("طريقة الدفع:", self.payment_method_combo)

        # المستلم
        self.receiver_edit = QLineEdit()
        self.receiver_edit.setPlaceholderText("اسم مستلم الدفعة (اختياري)")
        self.receiver_edit.setAlignment(Qt.AlignCenter)
        form_layout.addRow("المستلم:", self.receiver_edit)

        # الخصم
        self.discount_edit = QLineEdit()
        self.discount_edit.setPlaceholderText("0.00")
        self.discount_edit.setAlignment(Qt.AlignCenter)
        form_layout.addRow("الخصم:", self.discount_edit)

    def create_contract_payment_form(self, form_layout):
        """إنشاء نموذج دفعات العقود"""
        # اختيار العقد (إذا لم يكن محدد)
        if not self.entity_id:
            form_layout.addRow(QLabel("اختيار العقد:"))
            self.contract_combo = QComboBox()
            self.contract_combo.setEditable(True)
            self.contract_combo.lineEdit().setAlignment(Qt.AlignCenter)
            self.load_contracts_combo()
            form_layout.addRow("العقد:", self.contract_combo)
        else:
            # عرض موضوع العقد المحدد
            contract_subject = self.entity_data.get('موضوع_العقد', f'عقد رقم {self.entity_id}')
            contract_label = QLabel(contract_subject)
            contract_label.setStyleSheet("font-weight: bold; color: #2980b9;")
            form_layout.addRow("العقد:", contract_label)

        # وصف الدفعة
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف الدفعة")
        self.description_edit.setAlignment(Qt.AlignCenter)
        form_layout.addRow("وصف الدفعة:", self.description_edit)

        # المبلغ
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("أدخل مبلغ الدفعة")
        self.amount_edit.setAlignment(Qt.AlignCenter)
        form_layout.addRow("المبلغ:", self.amount_edit)

        # تاريخ الدفعة
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setDate(QDate.currentDate())
        self.payment_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ الدفعة:", self.payment_date_edit)

        # حالة الدفعة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["غير مدفوعة", "مدفوعة", "مؤجلة", "ملغاة"])
        form_layout.addRow("حالة الدفعة:", self.status_combo)

        # تاريخ السداد الفعلي
        self.actual_payment_date_edit = QDateEdit()
        self.actual_payment_date_edit.setDate(QDate.currentDate())
        self.actual_payment_date_edit.setCalendarPopup(True)
        self.actual_payment_date_edit.setEnabled(False)  # يفعل عند اختيار "مدفوعة"
        form_layout.addRow("تاريخ السداد الفعلي:", self.actual_payment_date_edit)

        # ربط تغيير الحالة بتفعيل تاريخ السداد
        self.status_combo.currentTextChanged.connect(self.on_status_changed)

        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("ملاحظات إضافية (اختياري)")
        self.notes_edit.setMaximumHeight(80)
        form_layout.addRow("ملاحظات:", self.notes_edit)


    def create_buttons(self, parent_layout):
        """إنشاء أزرار الحفظ والإلغاء"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)

        # زر الحفظ
        save_text = "حفظ التعديل" if self.is_edit_mode else "إضافة الدفعة"
        self.save_btn = QPushButton(qta.icon('fa5s.save', color='darkgreen'), save_text)
        self.save_btn.setMinimumSize(120, 40)
        self.save_btn.setObjectName(save_text)  # مهم للتنقل
        self.save_btn.clicked.connect(self.save_payment)
        buttons_layout.addWidget(self.save_btn)

        # زر الإلغاء
        cancel_btn = QPushButton(qta.icon('fa5s.times', color='crimson'), "إلغاء")
        cancel_btn.setMinimumSize(100, 40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        # إضافة مساحة مرنة
        buttons_layout.addStretch()

        parent_layout.addWidget(buttons_frame)

        # تطبيق التنقل بين الحقول
        self.setup_navigation()

    def setup_navigation(self):
        """إعداد التنقل بين الحقول"""
        if self.payment_type == "project":
            focus_widgets = [
                self.amount_edit,
                self.description_edit,
                self.payment_date_edit,
                self.payment_method_combo,
                self.receiver_edit,
                self.discount_edit
            ]
            if not self.entity_id:
                focus_widgets.insert(0, self.project_combo)
        else:
            focus_widgets = [
                self.description_edit,
                self.amount_edit,
                self.payment_date_edit,
                self.status_combo,
                self.notes_edit
            ]
            if not self.entity_id:
                focus_widgets.insert(0, self.contract_combo)

        save_text = "حفظ التعديل" if self.is_edit_mode else "إضافة الدفعة"
        apply_enter_focus(self, save_text, focus_widgets)

        # تركيز على أول حقل
        if focus_widgets:
            focus_widgets[0].setFocus()

    def on_status_changed(self, status):
        """معالجة تغيير حالة الدفعة"""
        if hasattr(self, 'actual_payment_date_edit'):
            self.actual_payment_date_edit.setEnabled(status == "مدفوعة")

    def load_projects_combo(self):
        """تحميل قائمة المشاريع"""
        try:
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            cursor.execute("SELECT id, اسم_المشروع FROM المشاريع ORDER BY اسم_المشروع")
            projects = cursor.fetchall()

            self.project_combo.clear()
            for project_id, project_name in projects:
                self.project_combo.addItem(f"{project_name} (ID: {project_id})", project_id)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل المشاريع: {e}")

    def load_contracts_combo(self):
        """تحميل قائمة العقود"""
        try:
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            cursor.execute("SELECT id, موضوع_العقد FROM العقود ORDER BY موضوع_العقد")
            contracts = cursor.fetchall()

            self.contract_combo.clear()
            for contract_id, contract_subject in contracts:
                self.contract_combo.addItem(f"{contract_subject} (ID: {contract_id})", contract_id)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل العقود: {e}")

    def load_payment_data(self):
        """تحميل بيانات الدفعة للتعديل"""
        if not self.payment_data:
            return

        try:
            if self.payment_type == "project":
                # تحميل بيانات مدفوعات المشاريع
                self.amount_edit.setText(str(self.payment_data.get('amount', '')))
                self.description_edit.setText(self.payment_data.get('description', ''))

                payment_date = self.payment_data.get('payment_date')
                if payment_date:
                    if isinstance(payment_date, str):
                        date_obj = QDate.fromString(payment_date, "yyyy-MM-dd")
                    else:
                        date_obj = QDate(payment_date)
                    self.payment_date_edit.setDate(date_obj)

                self.payment_method_combo.setCurrentText(self.payment_data.get('payment_method', ''))
                self.receiver_edit.setText(self.payment_data.get('receiver', ''))
                self.discount_edit.setText(str(self.payment_data.get('discount', '')))

            else:
                # تحميل بيانات دفعات العقود
                self.description_edit.setText(self.payment_data.get('description', ''))
                self.amount_edit.setText(str(self.payment_data.get('amount', '')))

                payment_date = self.payment_data.get('payment_date')
                if payment_date:
                    if isinstance(payment_date, str):
                        date_obj = QDate.fromString(payment_date, "yyyy-MM-dd")
                    else:
                        date_obj = QDate(payment_date)
                    self.payment_date_edit.setDate(date_obj)

                self.status_combo.setCurrentText(self.payment_data.get('status', 'غير مدفوعة'))

                actual_date = self.payment_data.get('actual_payment_date')
                if actual_date:
                    if isinstance(actual_date, str):
                        date_obj = QDate.fromString(actual_date, "yyyy-MM-dd")
                    else:
                        date_obj = QDate(actual_date)
                    self.actual_payment_date_edit.setDate(date_obj)

                self.notes_edit.setPlainText(self.payment_data.get('notes', ''))

        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل بعض البيانات:\n{str(e)}")

    def save_payment(self):
        """حفظ الدفعة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_data():
                return

            # جمع البيانات
            payment_data = self.collect_data()

            # حفظ في قاعدة البيانات
            if self.save_to_database(payment_data):
                QMessageBox.information(self, "نجاح", "تم حفظ الدفعة بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الدفعة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الدفعة:\n{str(e)}")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        # التحقق من المبلغ
        try:
            amount = float(self.amount_edit.text().strip())
            if amount <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ أكبر من صفر")
                self.amount_edit.setFocus()
                return False
        except ValueError:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            self.amount_edit.setFocus()
            return False

        # التحقق من الوصف للعقود
        if self.payment_type == "contract":
            if not self.description_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال وصف الدفعة")
                self.description_edit.setFocus()
                return False

        # التحقق من اختيار المشروع/العقد
        if not self.entity_id:
            if self.payment_type == "project" and self.project_combo.currentData() is None:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مشروع")
                return False
            elif self.payment_type == "contract" and self.contract_combo.currentData() is None:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عقد")
                return False

        return True

    def collect_data(self):
        """جمع البيانات من النموذج"""
        data = {
            'amount': float(self.amount_edit.text().strip()),
            'description': self.description_edit.text().strip(),
            'payment_date': self.payment_date_edit.date().toString("yyyy-MM-dd"),
            'user': 'admin'  # يمكن الحصول عليه من النظام
        }

        if self.payment_type == "project":
            data.update({
                'project_id': self.entity_id or self.project_combo.currentData(),
                'payment_method': self.payment_method_combo.currentText(),
                'receiver': self.receiver_edit.text().strip(),
                'discount': float(self.discount_edit.text().strip() or '0')
            })
        else:
            data.update({
                'contract_id': self.entity_id or self.contract_combo.currentData(),
                'status': self.status_combo.currentText(),
                'actual_payment_date': self.actual_payment_date_edit.date().toString("yyyy-MM-dd") if self.status_combo.currentText() == "مدفوعة" else None,
                'notes': self.notes_edit.toPlainText().strip()
            })

        return data

    def save_to_database(self, data):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            if self.payment_type == "project":
                if self.is_edit_mode:
                    # تحديث مدفوعات المشاريع
                    cursor.execute("""
                        UPDATE المشاريع_المدفوعات SET
                        المبلغ_المدفوع = %s, وصف_المدفوع = %s, تاريخ_الدفع = %s,
                        طريقة_الدفع = %s, المستلم = %s, خصم = %s, المستخدم = %s
                        WHERE id = %s
                    """, (
                        data['amount'], data['description'], data['payment_date'],
                        data['payment_method'], data['receiver'], data['discount'],
                        data['user'], self.payment_data['id']
                    ))
                else:
                    # إضافة مدفوعات المشاريع جديدة
                    cursor.execute("""
                        INSERT INTO المشاريع_المدفوعات
                        (معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع,
                         طريقة_الدفع, المستلم, خصم, المستخدم)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        data['project_id'], data['amount'], data['description'],
                        data['payment_date'], data['payment_method'], data['receiver'],
                        data['discount'], data['user']
                    ))
            else:
                if self.is_edit_mode:
                    # تحديث دفعات العقود
                    cursor.execute("""
                        UPDATE دفعات_العقود SET
                        وصف_الدفعة = %s, المبلغ = %s, تاريخ_الدفعة = %s,
                        حالة_الدفعة = %s, تاريخ_السداد = %s, ملاحظات = %s, المستخدم = %s
                        WHERE id = %s
                    """, (
                        data['description'], data['amount'], data['payment_date'],
                        data['status'], data['actual_payment_date'], data['notes'],
                        data['user'], self.payment_data['id']
                    ))
                else:
                    # إضافة دفعات العقود جديدة
                    cursor.execute("""
                        INSERT INTO دفعات_العقود
                        (معرف_العقد, وصف_الدفعة, المبلغ, تاريخ_الدفعة,
                         حالة_الدفعة, تاريخ_السداد, ملاحظات, المستخدم)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        data['contract_id'], data['description'], data['amount'],
                        data['payment_date'], data['status'], data['actual_payment_date'],
                        data['notes'], data['user']
                    ))

            conn.commit()
            cursor.close()
            conn.close()

            return True

        except Exception as e:
            print(f"خطأ في حفظ الدفعة: {e}")
            return False


# دالة لفتح نافذة إدارة المدفوعات
def open_payments_management_window(parent=None, payment_type="all", entity_id=None, entity_data=None):
    """فتح نافذة إدارة المدفوعات الشاملة"""
    try:
        window = PaymentsManagementWindow(parent, payment_type, entity_id, entity_data)
        window.show()
        return window
    except Exception as e:
        QMessageBox.critical(parent, "خطأ", f"فشل في فتح نافذة إدارة المدفوعات:\n{str(e)}")
        return None


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("WindowsVista")

    window = PaymentsManagementWindow()
    window.show()

    sys.exit(app.exec())
